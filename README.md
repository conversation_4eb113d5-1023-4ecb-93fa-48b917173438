# AI Hedge Fund

这是一个AI驱动的对冲基金概念验证项目。该项目的目标是探索使用人工智能进行交易决策。本项目**仅供教育目的**使用，不适用于实际交易或投资。

系统由多个协同工作的智能代理组成：

1. Aswath Damodaran 代理 - 估值学院院长，专注于故事、数字和严格的估值
2. <PERSON> 代理 - 价值投资之父，只购买具有安全边际的隐藏宝石
3. Bill Ackman 代理 - 激进投资者，采取大胆立场并推动变革
4. <PERSON>hie Wood 代理 - 成长型投资女王，相信创新和颠覆的力量
5. Charlie Munger 代理 - 沃伦·巴菲特的合伙人，只以合理价格购买优质企业
6. <PERSON> Burry 代理 - 《大空头》中的逆势者，寻找深度价值
7. <PERSON> 代理 - 实用投资者，在日常企业中寻找"十倍股"
8. Phil Fisher 代理 - 细致的成长型投资者，使用深入的"小道消息"研究
9. <PERSON> Druckenmiller 代理 - 宏观传奇，寻找具有增长潜力的不对称机会
10. <PERSON> Buffett 代理 - 奥马哈的先知，寻找以合理价格购买的优质公司
11. 估值代理 - 计算股票的内在价值并生成交易信号
12. 情绪代理 - 分析市场情绪并生成交易信号
13. 基本面代理 - 分析基本面数据并生成交易信号
14. 技术面代理 - 分析技术指标并生成交易信号
15. 风险管理器 - 计算风险指标并设置仓位限制
16. 投资组合管理器 - 做出最终交易决策并生成订单
17. 反思分析师 - 分析投资决策质量并提供改进建议

<img width="1042" alt="Screenshot 2025-03-22 at 6 19 07 PM" src="https://github.com/user-attachments/assets/cbae3dcf-b571-490d-b0ad-3f0f035ac0d4" />


**注意**：系统模拟交易决策，不进行实际交易。

[![Twitter Follow](https://img.shields.io/twitter/follow/virattt?style=social)](https://twitter.com/virattt)

## 免责声明

本项目**仅供教育和研究目的**。

- 不适用于实际交易或投资
- 不提供投资建议或保证
- 创建者对财务损失不承担责任
- 投资决策请咨询财务顾问
- 过去的表现不代表未来的结果

使用本软件即表示您同意仅将其用于学习目的。

## 目录
- [项目概述](#项目概述)
- [系统架构](#系统架构)
- [数据流](#数据流)
- [安装指南](#安装指南)
  - [使用Poetry](#使用poetry)
  - [使用Docker](#使用docker)
- [使用说明](#使用说明)
  - [运行对冲基金](#运行对冲基金)
  - [运行回测器](#运行回测器)
- [代理详解](#代理详解)
  - [投资风格代理](#投资风格代理)
  - [分析代理](#分析代理)
  - [管理代理](#管理代理)
- [代理提示词](#代理提示词)
  - [投资风格代理提示词](#投资风格代理提示词)
  - [分析代理提示词](#分析代理提示词)
  - [管理代理提示词](#管理代理提示词)
- [项目结构](#项目结构)
- [贡献指南](#贡献指南)
- [功能请求](#功能请求)
- [许可证](#许可证)

## 项目概述

AI Hedge Fund 是一个创新的概念验证项目，旨在探索人工智能在金融投资决策中的应用。该系统利用多个专业化的AI代理，每个代理都模拟了著名投资者的投资风格和策略，共同协作做出投资决策。

主要特点：

1. **多代理协作系统**：17个不同的AI代理，每个代理专注于投资决策的不同方面
2. **模拟知名投资者**：代理模拟了Warren Buffett、Charlie Munger、Cathie Wood等知名投资者的投资风格
3. **综合分析能力**：结合基本面分析、技术分析、情绪分析和估值分析
4. **风险管理**：专门的风险管理代理评估和控制投资组合风险
5. **回测功能**：内置回测系统，可评估策略在历史数据上的表现
6. **可视化界面**：提供Web界面展示代理决策过程和结果
7. **支持多种LLM模型**：兼容OpenAI、Groq、Anthropic等多种大型语言模型

## 系统架构

AI Hedge Fund采用模块化架构设计，主要由以下几个核心组件构成：

1. **代理系统**：
   - 投资风格代理（如Warren Buffett、Cathie Wood等）
   - 分析代理（基本面、技术面、情绪、估值）
   - 管理代理（风险管理器、投资组合管理器）

2. **工作流引擎**：
   - 基于LangGraph构建的状态图工作流
   - 管理代理之间的信息流和决策流程

3. **数据层**：
   - 金融数据API集成
   - 本地缓存系统减少API调用

4. **LLM集成**：
   - 支持多种LLM提供商（OpenAI、Groq、Anthropic等）
   - 本地LLM支持（通过Ollama）

5. **Web界面**：
   - 前端：基于React和TypeScript构建
   - 后端：FastAPI提供API服务

## 数据流

AI Hedge Fund中的数据流如下：

1. **数据获取**：
   - 系统从Financial Datasets API获取股票价格、财务指标、内部交易和公司新闻
   - 数据通过本地缓存系统存储，减少重复API调用

2. **代理分析流程**：
   - 每个投资风格代理接收相关金融数据
   - 代理分析数据并生成投资信号（看涨、看跌或中性）
   - 分析结果包含信号、置信度和推理过程

3. **风险评估**：
   - 风险管理代理接收所有投资风格代理的信号
   - 计算风险指标并设置仓位限制
   - 生成风险调整后的投资建议

4. **投资组合决策**：
   - 投资组合管理器接收风险管理代理的输出和前一日的反思建议
   - 综合所有信号、风险评估和历史决策反思
   - 生成最终交易决策（买入、卖出、做空或平仓）

5. **反思分析**：
   - 反思分析师在交易决策完成后分析决策质量
   - 评估决策的合理性、信号利用情况和风险控制
   - 生成改进建议并保存到文件供下一日使用
   - 形成持续学习和改进的闭环机制

6. **回测流程**：
   - 回测器模拟在历史时间段内执行交易决策
   - 计算投资组合价值、回报率和风险指标
   - 生成性能报告和可视化结果

7. **Web界面数据流**：
   - 前端通过API请求后端服务
   - 后端执行代理工作流并返回结果
   - 使用事件流实时更新进度和结果

## 安装指南

### 使用Poetry

克隆仓库：
```bash
git clone https://github.com/virattt/ai-hedge-fund.git
cd ai-hedge-fund
```

1. 安装Poetry（如果尚未安装）：
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. 安装依赖：
```bash
poetry install
```

3. 设置环境变量：
```bash
# 创建API密钥的.env文件
`cp .env.example .env`
```

4. 设置API密钥：
```bash
# 用于运行OpenAI托管的LLM（gpt-4o, gpt-4o-mini等）
# 从https://platform.openai.com/获取OpenAI API密钥
OPENAI_API_KEY=your-openai-api-key

# 用于运行Groq托管的LLM（deepseek, llama3等）
# 从https://groq.com/获取Groq API密钥
# 支持多个API密钥以避免速率限制（推荐配置多个）
GROQ_API_KEY1=your-first-groq-api-key
GROQ_API_KEY2=your-second-groq-api-key
GROQ_API_KEY3=your-third-groq-api-key
GROQ_API_KEY4=your-fourth-groq-api-key

# 用于获取为对冲基金提供动力的金融数据
# 从https://financialdatasets.ai/获取Financial Datasets API密钥
FINANCIAL_DATASETS_API_KEY=your-financial-datasets-api-key
```

### 使用Docker

1. 确保您的系统上已安装Docker。如果没有，可以从[Docker官方网站](https://www.docker.com/get-started)下载。

2. 克隆仓库：
```bash
git clone https://github.com/virattt/ai-hedge-fund.git
cd ai-hedge-fund
```

3. 设置环境变量：
```bash
# 创建API密钥的.env文件
cp .env.example .env
```

4. 编辑.env文件，按上述说明添加API密钥。

5. 构建Docker镜像：
```bash
# 在Linux/Mac上：
./run.sh build

# 在Windows上：
run.bat build
```

**重要提示**：您必须设置`OPENAI_API_KEY`、`GROQ_API_KEY1-4`、`ANTHROPIC_API_KEY`或`DEEPSEEK_API_KEY`才能使对冲基金正常工作。如果您想使用所有提供商的LLM，则需要设置所有API密钥。

**GROQ API密钥轮换**：为了避免GROQ API的速率限制（每天1000次请求），建议配置多个GROQ API密钥（GROQ_API_KEY1到GROQ_API_KEY4）。系统会在遇到429错误时自动切换到下一个可用的API密钥。详细信息请参阅 [GROQ API轮换指南](docs/groq_api_rotation_guide.md)。

AAPL、GOOGL、MSFT、NVDA和TSLA的金融数据是免费的，不需要API密钥。

对于任何其他股票代码，您需要在.env文件中设置`FINANCIAL_DATASETS_API_KEY`。

## 使用说明

### 运行对冲基金

#### 使用Poetry
```bash
poetry run python src/main.py --ticker AAPL,MSFT,NVDA
```

#### 使用Docker
```bash
# 在Linux/Mac上：
./run.sh --ticker AAPL,MSFT,NVDA main

# 在Windows上：
run.bat --ticker AAPL,MSFT,NVDA main
```

**示例输出：**
<img width="992" alt="Screenshot 2025-01-06 at 5 50 17 PM" src="https://github.com/user-attachments/assets/e8ca04bf-9989-4a7d-a8b4-34e04666663b" />

您还可以指定`--ollama`标志，使用本地LLM运行AI对冲基金。

```bash
# 使用Poetry：
poetry run python src/main.py --ticker AAPL,MSFT,NVDA --ollama

# 使用Docker（在Linux/Mac上）：
./run.sh --ticker AAPL,MSFT,NVDA --ollama main

# 使用Docker（在Windows上）：
run.bat --ticker AAPL,MSFT,NVDA --ollama main
```

您还可以指定`--show-reasoning`标志，将每个代理的推理过程打印到控制台。

```bash
# 使用Poetry：
poetry run python src/main.py --ticker AAPL,MSFT,NVDA --show-reasoning

# 使用Docker（在Linux/Mac上）：
./run.sh --ticker AAPL,MSFT,NVDA --show-reasoning main

# 使用Docker（在Windows上）：
run.bat --ticker AAPL,MSFT,NVDA --show-reasoning main
```

您可以选择指定开始和结束日期，为特定时间段做出决策。

```bash
# 使用Poetry：
poetry run python src/main.py --ticker AAPL,MSFT,NVDA --start-date 2024-01-01 --end-date 2024-03-01

# 使用Docker（在Linux/Mac上）：
./run.sh --ticker AAPL,MSFT,NVDA --start-date 2024-01-01 --end-date 2024-03-01 main

# 使用Docker（在Windows上）：
run.bat --ticker AAPL,MSFT,NVDA --start-date 2024-01-01 --end-date 2024-03-01 main
```

### 运行回测器

#### 使用Poetry
```bash
poetry run python src/backtester.py --ticker AAPL,MSFT,NVDA
```

#### 使用Docker
```bash
# 在Linux/Mac上：
./run.sh --ticker AAPL,MSFT,NVDA backtest

# 在Windows上：
run.bat --ticker AAPL,MSFT,NVDA backtest
```

**示例输出：**
<img width="941" alt="Screenshot 2025-01-06 at 5 47 52 PM" src="https://github.com/user-attachments/assets/00e794ea-8628-44e6-9a84-8f8a31ad3b47" />


您可以选择指定开始和结束日期，在特定时间段内进行回测。

```bash
# 使用Poetry：
poetry run python src/backtester.py --ticker AAPL,MSFT,NVDA --start-date 2024-01-01 --end-date 2024-03-01

# 使用Docker（在Linux/Mac上）：
./run.sh --ticker AAPL,MSFT,NVDA --start-date 2024-01-01 --end-date 2024-03-01 backtest

# 使用Docker（在Windows上）：
run.bat --ticker AAPL,MSFT,NVDA --start-date 2024-01-01 --end-date 2024-03-01 backtest
```

您还可以指定`--ollama`标志，使用本地LLM运行回测器。
```bash
# 使用Poetry：
poetry run python src/backtester.py --ticker AAPL,MSFT,NVDA --ollama

# 使用Docker（在Linux/Mac上）：
./run.sh --ticker AAPL,MSFT,NVDA --ollama backtest

# 使用Docker（在Windows上）：
run.bat --ticker AAPL,MSFT,NVDA --ollama backtest
```


## 代理详解

AI对冲基金系统由多个专业化的代理组成，每个代理都有其独特的功能和投资风格。以下是对每个代理的详细介绍：

### 投资风格代理

#### 1. Warren Buffett 代理
- **主要功能**：模拟巴菲特的价值投资策略，寻找具有经济护城河的优质公司
- **分析方法**：
  - 分析公司基本面、经济护城河和管理质量
  - 计算内在价值并与市场价格比较
  - 寻找具有安全边际的长期投资机会
- **交互方式**：向投资组合管理器提供看涨/看跌/中性信号及置信度
- **特点**：注重长期持有，偏好稳定、可预测的现金流企业

#### 2. Charlie Munger 代理
- **主要功能**：应用芒格的多元思维模型和理性投资方法
- **分析方法**：
  - 使用跨学科思维模型评估投资机会
  - 分析商业模式的质量和竞争优势
  - 评估管理层的诚信和能力
- **交互方式**：与其他代理协作，特别是与Warren Buffett代理互补
- **特点**：强调避免愚蠢决策，寻找高质量企业，注重长期价值

#### 3. Ben Graham 代理
- **主要功能**：应用格雷厄姆的价值投资原则，寻找被低估的股票
- **分析方法**：
  - 计算净净营运资本和有形账面价值
  - 应用安全边际原则评估投资风险
  - 分析财务比率如市盈率、市净率和债务水平
- **交互方式**：提供基于定量分析的投资信号
- **特点**：严格的价值标准，偏好统计上被低估的股票

#### 4. Phil Fisher 代理
- **主要功能**：应用费雪的成长型投资策略，寻找具有长期增长潜力的公司
- **分析方法**：
  - 进行"小道消息"研究，分析公司产品和服务
  - 评估管理层质量和研发投入
  - 分析销售组织和长期增长前景
- **交互方式**：提供基于质化分析的成长型投资信号
- **特点**：注重质量而非价格，寻找能够长期持有的优质成长股

#### 5. Peter Lynch 代理
- **主要功能**：应用林奇的实用投资方法，寻找日常生活中的投资机会
- **分析方法**：
  - 分析公司增长潜力和市场定位
  - 评估PEG比率（市盈率相对于增长率）
  - 根据公司类型（稳定增长、周期性、转机型等）调整策略
- **交互方式**：提供基于公司类型和增长前景的投资信号
- **特点**：注重实用性，偏好容易理解的业务模式

#### 6. Cathie Wood 代理
- **主要功能**：模拟伍德的创新投资策略，关注颠覆性技术
- **分析方法**：
  - 评估技术创新和市场颠覆潜力
  - 分析增长率和市场渗透率
  - 预测长期技术趋势和采用曲线
- **交互方式**：提供基于创新潜力的高增长投资信号
- **特点**：高风险高回报，关注未来5-10年的技术趋势

#### 7. Bill Ackman 代理
- **主要功能**：模拟阿克曼的激进投资策略，寻找可改善的优质企业
- **分析方法**：
  - 评估公司治理和管理效率
  - 分析业务质量和改善空间
  - 计算内在价值和激活价值的差距
- **交互方式**：提供基于公司改善潜力的投资信号
- **特点**：集中持仓，积极参与公司治理，寻求催化剂

#### 8. Michael Burry 代理
- **主要功能**：模拟伯里的逆势投资策略，寻找市场错误定价
- **分析方法**：
  - 深入分析财务报表中被忽视的信息
  - 寻找市场情绪与基本面的脱节
  - 评估系统性风险和宏观经济趋势
- **交互方式**：提供基于深度价值和市场错误定价的投资信号
- **特点**：逆向思维，耐心等待，关注被忽视的风险

#### 9. Aswath Damodaran 代理
- **主要功能**：应用达摩达兰的估值方法，计算股票内在价值
- **分析方法**：
  - 使用DCF模型计算公司内在价值
  - 分析资本成本和再投资效率
  - 评估风险溢价和增长预期
- **交互方式**：提供基于严格估值的投资信号
- **特点**：注重数据和故事的结合，强调估值的系统性方法

#### 10. Stanley Druckenmiller 代理
- **主要功能**：模拟德鲁肯米勒的宏观投资策略，寻找不对称风险回报机会
- **分析方法**：
  - 分析增长动量和市场情绪
  - 评估风险回报比和资本保全
  - 寻找具有增长潜力的行业领导者
- **交互方式**：提供基于宏观趋势和动量的投资信号
- **特点**：愿意为真正的增长领导者支付更高估值，在高确信度时积极进取

### 分析代理

#### 11. 基本面分析代理
- **主要功能**：分析公司财务指标和基本面数据
- **分析方法**：
  - 评估盈利能力（ROE、净利率、营业利润率）
  - 分析增长指标（收入增长、EPS增长）
  - 评估财务健康状况（流动比率、债务权益比）
  - 分析估值比率（市盈率、市净率、市销率）
- **交互方式**：向投资组合管理器提供基于财务指标的综合信号
- **特点**：纯定量分析，不受投资风格影响

#### 12. 技术分析代理
- **主要功能**：分析价格走势和技术指标
- **分析方法**：
  - 趋势跟踪（移动平均线、MACD）
  - 均值回归（相对强弱指数、布林带）
  - 动量分析（价格动量、成交量）
  - 波动性分析（ATR、波动率）
  - 统计套利信号
- **交互方式**：向投资组合管理器提供基于技术指标的交易信号
- **特点**：完全基于价格和交易数据，不考虑基本面

#### 13. 情绪分析代理
- **主要功能**：分析市场情绪和投资者行为
- **分析方法**：
  - 分析内部交易活动（高管买卖）
  - 评估公司新闻情绪（正面/负面）
  - 计算情绪指标和异常活动
- **交互方式**：向投资组合管理器提供基于市场情绪的信号
- **特点**：关注短期市场情绪变化和异常活动

#### 14. 估值分析代理
- **主要功能**：计算股票内在价值和相对估值
- **分析方法**：
  - 实现四种互补的估值方法
  - 计算DCF估值和相对估值
  - 分析估值与市场价格的差距
- **交互方式**：向投资组合管理器提供基于估值的投资信号
- **特点**：注重客观估值，不受投资风格影响

### 管理代理

#### 15. 风险管理代理
- **主要功能**：评估投资组合风险并设置仓位限制
- **分析方法**：
  - 计算每个股票的仓位限制（最大为投资组合的20%）
  - 考虑现有仓位和可用现金
  - 确保风险分散和资本保全
- **交互方式**：向投资组合管理器提供风险调整后的仓位限制
- **特点**：保守的风险管理，优先考虑资本保全

#### 16. 投资组合管理代理
- **主要功能**：综合所有代理信号，做出最终交易决策
- **分析方法**：
  - 整合所有投资风格代理和分析代理的信号
  - 考虑每个信号的置信度和相关性
  - 应用风险管理代理的仓位限制
  - 参考前一日反思分析师的建议和改进意见
  - 生成最终交易决策（买入、卖出、做空、平仓或持有）
- **交互方式**：作为最终决策者，生成可执行的交易订单
- **特点**：平衡不同投资风格和分析方法，具备学习和改进能力

#### 17. 反思分析代理
- **主要功能**：分析投资组合管理器的决策质量，提供改进建议
- **分析方法**：
  - 评估投资决策是否充分考虑了各分析师信号
  - 分析决策的逻辑一致性和风险管理水平
  - 识别决策中的优点和潜在问题
  - 生成具体的改进建议和关键洞察
  - 对决策质量进行评分（excellent/good/fair/poor）
- **交互方式**：在交易决策完成后运行，将反思结果保存到文件供下一日使用
- **特点**：形成持续学习闭环，帮助系统不断改进决策质量

## 🔄 反思学习机制

本系统引入了创新的反思学习机制，通过反思分析师实现持续改进：

### 工作原理
1. **每日交易结束后**：反思分析师自动分析投资组合管理器的决策
2. **质量评估**：从四个维度评估决策质量（excellent/good/fair/poor）
3. **生成建议**：提供具体的改进建议和关键洞察
4. **持久化存储**：将反思结果保存到 `reasoning_logs/reflections/` 目录
5. **下日应用**：投资组合管理器在下一日自动加载前一日的反思建议

### 反思分析内容
- **决策质量评级**：excellent, good, fair, poor
- **正确性评分**：0-100分的量化评估
- **关键洞察**：识别决策中的优点和问题
- **改进建议**：针对性的具体建议
- **详细推理**：完整的分析过程

### 文件结构
```
reasoning_logs/
└── reflections/
    ├── reflection_2024-01-01.json
    ├── reflection_2024-01-02.json
    └── ...
```

### 学习效果
- **首日**：无前一日反思数据，按正常流程执行
- **第二日起**：投资组合管理器接收前一日反思建议，改进决策质量
- **持续改进**：形成"决策→反思→改进→决策"的闭环学习机制

## 代理提示词

本节详细介绍了每个代理使用的提示词模板，这些模板指导代理如何分析数据并做出投资决策。提示词是代理"个性"和投资风格的核心，决定了代理如何解释数据并形成观点。

### 投资风格代理提示词

#### Warren Buffett 代理提示词

**System 提示词**：
```
You are a Warren Buffett AI agent. Decide on investment signals based on Warren Buffett's principles:
- Circle of Competence: Only invest in businesses you understand
- Margin of Safety (> 30%): Buy at a significant discount to intrinsic value
- Economic Moat: Look for durable competitive advantages
- Quality Management: Seek conservative, shareholder-oriented teams
- Financial Strength: Favor low debt, strong returns on equity
- Long-term Horizon: Invest in businesses, not just stocks
...
```

*中文翻译：你是一个沃伦·巴菲特AI代理。基于巴菲特的原则做出投资信号：能力圈（只投资你理解的企业）、安全边际（>30%，以显著低于内在价值的价格购买）、经济护城河（寻找持久的竞争优势）、优质管理层（寻找保守的、以股东为导向的团队）、财务实力（偏好低债务、高股本回报率）、长期视角（投资企业，而非仅仅是股票）...*

**Human 提示词**：
```
Based on the following data, create the investment signal as Warren Buffett would:

Analysis Data for {ticker}:
{analysis_data}

Return the trading signal in the following JSON format exactly:
{
  "signal": "bullish" | "bearish" | "neutral",
  "confidence": float between 0 and 100,
  "reasoning": "string"
}
```

*中文翻译：基于以下数据，创建沃伦·巴菲特风格的投资信号：分析数据为{ticker}：{analysis_data}。请以以下JSON格式精确返回交易信号：{"signal": "bullish" | "bearish" | "neutral", "confidence": 0到100之间的浮点数, "reasoning": "字符串"}*

#### Charlie Munger 代理提示词

**System 提示词**：
```
You are a Charlie Munger AI agent, making investment decisions using his principles:
...
Rules:
- Praise businesses with predictable, consistent operations and cash flows.
- Value businesses with high ROIC and pricing power.
- Prefer simple businesses with understandable economics.
- Admire management with skin in the game and shareholder-friendly capital allocation.
- Focus on long-term economics rather than short-term metrics.
- Be skeptical of businesses with rapidly changing dynamics or excessive share dilution.
- Avoid excessive leverage or financial engineering.
- Provide a rational, data-driven recommendation (bullish, bearish, or neutral).
...
```

*中文翻译：你是一个查理·芒格AI代理，使用他的原则做出投资决策：...规则：赞赏业务可预测、稳定的运营和现金流的企业；重视具有高投资回报率和定价能力的企业；偏好经济学简单易懂的企业；欣赏有利益相关和对股东友好的资本配置的管理层；关注长期经济效益而非短期指标；对业务快速变化或过度股份稀释的企业持怀疑态度；避免过度杠杆或金融工程；提供理性、数据驱动的建议（看涨、看跌或中性）...*

#### Ben Graham 代理提示词

**System 提示词**：
```
You are a Benjamin Graham AI agent, making investment decisions using his principles:
1. Insist on a margin of safety by buying below intrinsic value (e.g., using Graham Number, net-net).
2. Emphasize the company's financial strength (low leverage, ample current assets).
3. Prefer stable earnings over multiple years.
4. Consider dividend record for extra safety.
5. Avoid speculative or high-growth assumptions; focus on proven metrics.
...
```

*中文翻译：你是一个本杰明·格雷厄姆AI代理，使用他的原则做出投资决策：1. 坚持安全边际，以低于内在价值的价格购买（例如，使用格雷厄姆数字、净净值）；2. 强调公司的财务实力（低杠杆、充足的流动资产）；3. 偏好多年稳定的收益；4. 考虑股息记录以增加安全性；5. 避免投机或高增长假设；专注于已证实的指标...*

#### Phil Fisher 代理提示词

**System 提示词**：
```
You are a Phil Fisher AI agent, making investment decisions using his principles:

1. Emphasize long-term growth potential and quality of management.
2. Focus on companies investing in R&D for future products/services.
3. Look for strong profitability and consistent margins.
4. Willing to pay more for exceptional companies but still mindful of valuation.
5. Rely on thorough research (scuttlebutt) and thorough fundamental checks.
...
```

*中文翻译：你是一个菲利普·费舍尔AI代理，使用他的原则做出投资决策：1. 强调长期增长潜力和管理质量；2. 关注投资研发以开发未来产品/服务的公司；3. 寻找强大的盈利能力和稳定的利润率；4. 愿意为卓越公司支付更高价格，但仍注意估值；5. 依靠彻底的研究（小道消息）和全面的基本面检查...*

#### Peter Lynch 代理提示词

**System 提示词**：
```
You are a Peter Lynch AI agent. You make investment decisions based on Peter Lynch's well-known principles:
...
When you provide your reasoning, do it in Peter Lynch's voice:
- Cite the PEG ratio
- Mention 'ten-bagger' potential if applicable
- Refer to personal or anecdotal observations (e.g., "If my kids love the product...")
- Use practical, folksy language
- Provide key positives and negatives
- Conclude with a clear stance (bullish, bearish, or neutral)
```

*中文翻译：你是一个彼得·林奇AI代理。你基于彼得·林奇著名的原则做出投资决策：...当你提供推理时，使用彼得·林奇的语气：引用PEG比率；如果适用，提及"十倍股"潜力；引用个人或轶事观察（例如，"如果我的孩子喜欢这个产品..."）；使用实用、通俗的语言；提供关键的正面和负面因素；以明确的立场结束（看涨、看跌或中性）*

#### Cathie Wood 代理提示词

**System 提示词**：
```
You are a Cathie Wood AI agent, making investment decisions using her principles:

1. Seek companies leveraging disruptive innovation.
2. Emphasize exponential growth potential, large TAM.
3. Focus on technology, healthcare, or other future-facing sectors.
4. Consider multi-year time horizons for potential breakthroughs.
5. Accept higher volatility in pursuit of high returns.
6. Evaluate management's vision and ability to invest in R&D.
...
```

*中文翻译：你是一个凯茜·伍德AI代理，使用她的原则做出投资决策：1. 寻找利用颠覆性创新的公司；2. 强调指数级增长潜力，大型总可寻址市场；3. 专注于技术、医疗保健或其他面向未来的行业；4. 考虑多年时间范围内的潜在突破；5. 接受更高的波动性以追求高回报；6. 评估管理层的愿景和投资研发的能力...*

#### Bill Ackman 代理提示词

**System 提示词**：
```
You are a Bill Ackman AI agent, making investment decisions using his principles:

1. Seek high-quality businesses with durable competitive advantages (moats), often in well-known consumer or service brands.
2. Prioritize consistent free cash flow and growth potential over the long term.
3. Advocate for strong financial discipline (reasonable leverage, efficient capital allocation).
4. Valuation matters: target intrinsic value with a margin of safety.
5. Consider activism where management or operational improvements can unlock substantial upside.
6. Concentrate on a few high-conviction investments.
...
```

*中文翻译：你是一个比尔·阿克曼AI代理，使用他的原则做出投资决策：1. 寻找具有持久竞争优势（护城河）的高质量企业，通常是知名消费品或服务品牌；2. 优先考虑长期稳定的自由现金流和增长潜力；3. 倡导强大的财务纪律（合理的杠杆，高效的资本配置）；4. 估值很重要：以安全边际为目标的内在价值；5. 考虑激进主义，通过管理或运营改进释放巨大上升空间；6. 集中于少数高确信度投资...*

#### Michael Burry 代理提示词

**System 提示词**：
```
You are an AI agent emulating Dr. Michael J. Burry. Your mandate:
- Hunt for deep value in US equities using hard numbers (free cash flow, EV/EBIT, balance sheet)
- Be contrarian: hatred in the press can be your friend if fundamentals are solid
- Focus on downside first – avoid leveraged balance sheets
- Look for hard catalysts such as insider buying, buybacks, or asset sales
- Communicate in Burry's terse, data‑driven style
...
```

*中文翻译：你是一个模拟迈克尔·J·伯里博士的AI代理。你的任务是：使用硬数据（自由现金流、企业价值/息税前利润、资产负债表）在美国股票中寻找深度价值；保持逆向思维：如果基本面坚实，媒体的负面评价可能是你的朋友；首先关注下行风险 - 避免高杠杆资产负债表；寻找明确的催化剂，如内部人士购买、股票回购或资产出售；以伯里简洁、数据驱动的风格沟通...*

#### Aswath Damodaran 代理提示词

**System 提示词**：
```
You are Aswath Damodaran, Professor of Finance at NYU Stern.
Use your valuation framework to issue trading signals on US equities.

Speak with your usual clear, data‑driven tone:
  ◦ Start with the company "story" (qualitatively)
  ◦ Connect that story to key numerical drivers: revenue growth, margins, reinvestment, risk
  ◦ Conclude with value: your FCFF DCF estimate, margin of safety, and relative valuation sanity checks
  ◦ Highlight major uncertainties and how they affect value
Return ONLY the JSON specified below.
```

*中文翻译：你是纽约大学斯特恩商学院的金融学教授阿斯瓦斯·达摩达兰。使用你的估值框架对美国股票发出交易信号。以你惯常的清晰、数据驱动的语调发言：首先介绍公司的"故事"（定性）；将该故事与关键数字驱动因素联系起来：收入增长、利润率、再投资、风险；以价值结束：你的FCFF DCF估计、安全边际和相对估值合理性检查；强调主要不确定性及其如何影响价值。仅返回下面指定的JSON。*

#### Stanley Druckenmiller 代理提示词

**System 提示词**：
```
You are a Stanley Druckenmiller AI agent, making investment decisions using his principles:

1. Seek asymmetric risk-reward opportunities (large upside, limited downside).
2. Emphasize growth, momentum, and market sentiment.
3. Preserve capital by avoiding major drawdowns.
4. Willing to pay higher valuations for true growth leaders.
5. Be aggressive when conviction is high.
6. Cut losses quickly if the thesis changes.
...
```

*中文翻译：你是一个斯坦利·德鲁肯米勒AI代理，使用他的原则做出投资决策：1. 寻找不对称风险回报机会（大幅上涨潜力，有限下行风险）；2. 强调增长、动量和市场情绪；3. 通过避免重大回撤来保全资本；4. 愿意为真正的增长领导者支付更高估值；5. 在确信度高时积极进取；6. 如果投资论点改变，迅速止损...*

### 分析代理提示词

分析代理（基本面、技术面、情绪和估值分析代理）主要使用算法和定量分析，而非LLM提示词。这些代理通过直接计算和分析财务指标、价格数据、情绪信号和估值模型来生成交易信号，不依赖于特定的提示词模板。

### 管理代理提示词

#### 投资组合管理代理提示词

投资组合管理代理使用LLM来综合所有分析师信号并做出最终决策。其提示词指导LLM如何权衡不同信号、考虑风险限制，并生成最终交易决策。由于代码中未直接展示其完整提示词，但其核心功能是整合所有代理信号，应用风险限制，并生成最终交易决策（买入、卖出、做空、平仓或持有）。

## 项目结构

```
ai-hedge-fund/
├── src/                          # 源代码目录
│   ├── agents/                   # 代理定义和工作流
│   │   ├── aswath_damodaran.py   # Aswath Damodaran代理 - 估值专家
│   │   ├── ben_graham.py         # Ben Graham代理 - 价值投资之父
│   │   ├── bill_ackman.py        # Bill Ackman代理 - 激进投资者
│   │   ├── cathie_wood.py        # Cathie Wood代理 - 创新投资专家
│   │   ├── charlie_munger.py     # Charlie Munger代理 - 巴菲特合伙人
│   │   ├── fundamentals.py       # 基本面分析代理
│   │   ├── michael_burry.py      # Michael Burry代理 - 逆势投资者
│   │   ├── peter_lynch.py        # Peter Lynch代理 - 实用投资者
│   │   ├── phil_fisher.py        # Phil Fisher代理 - 成长型投资者
│   │   ├── portfolio_manager.py  # 投资组合管理代理 - 最终决策者
│   │   ├── reflection_analyst.py # 反思分析代理 - 决策质量分析
│   │   ├── risk_manager.py       # 风险管理代理 - 风险评估
│   │   ├── sentiment.py          # 情绪分析代理 - 市场情绪分析
│   │   ├── stanley_druckenmiller.py # Stanley Druckenmiller代理 - 宏观投资者
│   │   ├── technicals.py         # 技术分析代理 - 技术指标分析
│   │   ├── valuation.py          # 估值分析代理 - 内在价值计算
│   │   ├── warren_buffett.py     # Warren Buffett代理 - 价值投资者
│   ├── data/                     # 数据处理模块
│   │   ├── cache.py              # 本地缓存系统 - 减少API调用
│   │   ├── models.py             # 数据模型定义 - Pydantic模型
│   ├── graph/                    # 工作流图模块
│   │   ├── state.py              # 状态管理 - 代理状态和工作流状态
│   ├── llm/                      # LLM集成模块
│   │   ├── api_models.json       # API模型配置
│   │   ├── models.py             # LLM模型定义和工具
│   │   ├── ollama_models.json    # Ollama本地模型配置
│   ├── tools/                    # 代理工具
│   │   ├── api.py                # API工具 - 金融数据获取
│   ├── utils/                    # 实用工具
│   │   ├── analysts.py           # 分析师工具 - 代理配置
│   │   ├── display.py            # 显示工具 - 结果格式化
│   │   ├── docker.py             # Docker工具 - 容器支持
│   │   ├── llm.py                # LLM工具 - 模型调用
│   │   ├── ollama.py             # Ollama工具 - 本地LLM支持
│   │   ├── progress.py           # 进度工具 - 进度跟踪
│   │   ├── visualize.py          # 可视化工具 - 图表生成
│   ├── backtester.py             # 回测工具 - 策略回测
│   ├── main.py                   # 主入口点 - 程序启动
├── app/                          # Web应用目录
│   ├── backend/                  # 后端API
│   │   ├── models/               # 后端数据模型
│   │   │   ├── events.py         # 事件模型 - SSE事件
│   │   │   ├── schemas.py        # 模式定义 - API请求/响应
│   │   ├── routes/               # API路由
│   │   │   ├── hedge_fund.py     # 对冲基金路由 - 主API端点
│   │   │   ├── health.py         # 健康检查路由
│   │   ├── services/             # 后端服务
│   │   │   ├── graph.py          # 图服务 - 工作流管理
│   │   │   ├── portfolio.py      # 投资组合服务 - 投资组合管理
│   │   ├── main.py               # 后端入口点 - FastAPI应用
│   ├── frontend/                 # 前端应用
│   │   ├── src/                  # 前端源代码
│   │   │   ├── components/       # React组件
│   │   │   │   ├── Flow.tsx      # 流程组件 - 代理流程图
│   │   │   │   ├── Layout.tsx    # 布局组件 - 页面布局
│   │   │   │   ├── sidebar/      # 侧边栏组件
│   │   │   │   ├── ui/           # UI组件库
│   │   │   ├── contexts/         # React上下文
│   │   │   ├── data/             # 前端数据
│   │   │   │   ├── agents.ts     # 代理定义
│   │   │   │   ├── models.ts     # 模型定义
│   │   │   ├── nodes/            # 流程图节点
│   │   │   ├── services/         # 前端服务
│   │   │   │   ├── api.ts        # API服务 - 后端通信
│   │   │   ├── App.tsx           # 主应用组件
│   │   │   ├── main.tsx          # 前端入口点
├── docker-compose.yml            # Docker Compose配置
├── Dockerfile                    # Docker构建文件
├── pyproject.toml                # Poetry项目配置
├── run.sh                        # Linux/Mac运行脚本
├── run.bat                       # Windows运行脚本
├── .env.example                  # 环境变量示例
```

### 核心文件功能说明

#### 主要入口点
- **src/main.py**: 对冲基金的主入口点，处理命令行参数，创建代理工作流，并执行交易决策。
- **src/backtester.py**: 回测系统的入口点，模拟在历史数据上执行交易策略。

#### 代理系统
- **src/agents/warren_buffett.py**: 实现了Warren Buffett的投资策略，分析公司基本面、经济护城河、管理质量，并计算内在价值。
- **src/agents/portfolio_manager.py**: 最终决策代理，综合所有分析师信号、风险评估和前一日反思建议，生成交易决策。
- **src/agents/reflection_analyst.py**: 反思分析代理，分析投资决策质量，提供改进建议，形成持续学习机制。
- **src/agents/risk_manager.py**: 风险管理代理，评估投资组合风险并设置仓位限制。

#### 数据处理
- **src/tools/api.py**: 提供与金融数据API的集成，获取股票价格、财务指标、内部交易和公司新闻。
- **src/data/cache.py**: 实现本地缓存系统，减少API调用并提高性能。
- **src/data/models.py**: 定义数据模型，使用Pydantic进行数据验证和序列化。

#### LLM集成
- **src/llm/models.py**: 定义LLM模型接口，支持多种LLM提供商。
- **src/utils/llm.py**: 提供LLM调用工具，处理提示工程和响应解析。
- **src/utils/ollama.py**: 支持本地LLM推理，通过Ollama集成开源模型。

#### 工作流引擎
- **src/graph/state.py**: 管理代理状态和工作流状态，实现基于LangGraph的状态图。
- **src/utils/analysts.py**: 配置分析师代理，定义代理顺序和工作流连接。

#### Web应用
- **app/backend/main.py**: FastAPI后端应用的入口点，提供API服务。
- **app/backend/routes/hedge_fund.py**: 对冲基金API路由，处理前端请求并返回结果。
- **app/frontend/src/App.tsx**: React前端应用的主组件，提供用户界面。
- **app/frontend/src/components/Flow.tsx**: 流程图组件，可视化代理工作流和决策过程。

## 贡献指南

1. Fork仓库
2. 创建功能分支
3. 提交您的更改
4. 推送到分支
5. 创建Pull Request

**重要提示**：请保持您的pull requests小而集中。这将使审查和合并更容易。

## 功能请求

如果您有功能请求，请打开一个[issue](https://github.com/virattt/ai-hedge-fund/issues)，并确保它被标记为`enhancement`。

## 许可证

本项目采用MIT许可证 - 详情请参阅LICENSE文件。
