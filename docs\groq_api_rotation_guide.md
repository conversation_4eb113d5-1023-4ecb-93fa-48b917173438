# GROQ API密钥轮换功能使用指南

## 概述

为了解决GROQ API的速率限制问题（每天1000次请求限制），我们实现了一个自动API密钥轮换系统。当遇到429错误（速率限制）时，系统会自动切换到下一个可用的API密钥，确保回测和其他操作能够持续进行。

## 功能特性

- **自动检测速率限制错误**: 识别GROQ API的429错误和相关错误消息
- **智能密钥轮换**: 自动切换到下一个可用的API密钥
- **失败状态管理**: 跟踪失败的API密钥，避免重复使用
- **自动恢复**: 当所有密钥都失败时，重置状态并重新开始
- **线程安全**: 支持多线程环境下的安全使用

## 配置方法

### 1. 在.env文件中配置多个GROQ API密钥

```bash
# 主要的GROQ API密钥
GROQ_API_KEY1=gsk_your_first_api_key_here
GROQ_API_KEY2=gsk_your_second_api_key_here  
GROQ_API_KEY3=gsk_your_third_api_key_here
GROQ_API_KEY4=gsk_your_fourth_api_key_here
```

### 2. 获取GROQ API密钥

1. 访问 [GROQ Console](https://console.groq.com/)
2. 注册多个账户（建议使用不同的邮箱）
3. 在每个账户中创建API密钥
4. 将密钥添加到.env文件中

## 使用方法

### 自动使用（推荐）

系统会自动使用API密钥轮换功能，无需额外配置。当运行回测时：

```bash
python src/backtester.py --tickers MSFT --track-accuracy --save-reasoning --save-input-data --start-date 2024-01-02 --end-date 2024-12-31 --use-local-news --news-sources alpha_vantage --news-time-offset 1
```

选择`[groq] llama-4-scout-17b`模型时，系统会自动处理API密钥轮换。

### 手动管理（高级用户）

```python
from src.utils.groq_api_manager import groq_api_manager

# 获取当前状态
status = groq_api_manager.get_status()
print(f"API状态: {status}")

# 手动切换密钥
groq_api_manager.handle_rate_limit_error("Rate limit reached")

# 重置失败状态
groq_api_manager.reset_failed_keys()
```

## 工作原理

### 1. 错误检测

系统监控以下错误模式：
- HTTP 429状态码
- "rate limit reached"消息
- "rate limit exceeded"消息
- "requests per day"限制消息

### 2. 密钥轮换流程

```
当前密钥 → 检测到429错误 → 标记密钥为失败 → 切换到下一个密钥 → 重新创建LLM实例 → 继续执行
```

### 3. 恢复机制

- 当所有密钥都失败时，系统会重置失败状态
- 重新从第一个密钥开始使用
- 这样可以处理临时的速率限制问题

## 日志和监控

### 查看轮换日志

系统会输出详细的日志信息：

```
检测到GROQ速率限制错误: Error code: 429...
已切换到新的GROQ API密钥，重新创建LLM实例...
LLM实例重新创建成功，继续重试...
```

### 监控API使用状态

```python
from src.utils.groq_api_manager import groq_api_manager

status = groq_api_manager.get_status()
print(f"总密钥数: {status['total_keys']}")
print(f"当前密钥索引: {status['current_key_index']}")
print(f"失败密钥数: {status['failed_keys_count']}")
print(f"可用密钥数: {status['available_keys_count']}")
```

## 最佳实践

### 1. 密钥配置建议

- **至少配置3个API密钥**: 确保有足够的备用密钥
- **使用不同账户**: 每个密钥来自不同的GROQ账户
- **定期更新**: 定期检查和更新API密钥

### 2. 使用建议

- **监控使用量**: 定期检查每个账户的API使用情况
- **合理安排任务**: 避免在短时间内进行大量API调用
- **保留日志**: 保存轮换日志以便问题排查

### 3. 错误处理

- 系统会自动处理大部分错误情况
- 如果所有密钥都失败，建议等待一段时间后重试
- 检查API密钥是否有效和账户状态

## 故障排除

### 常见问题

1. **没有找到API密钥**
   - 检查.env文件中的密钥配置
   - 确保密钥名称正确（GROQ_API_KEY1, GROQ_API_KEY2等）

2. **所有密钥都失败**
   - 检查每个账户的API配额使用情况
   - 等待配额重置（通常是24小时）
   - 验证API密钥是否仍然有效

3. **轮换不工作**
   - 检查错误消息是否包含速率限制关键词
   - 查看日志输出确认轮换逻辑是否触发

### 调试方法

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查API管理器状态
from src.utils.groq_api_manager import groq_api_manager
print(groq_api_manager.get_status())
```

## 技术实现

### 核心组件

1. **GroqApiManager**: API密钥管理器类
2. **groq_api_manager**: 全局单例实例
3. **修改的get_model()**: 使用API管理器获取密钥
4. **增强的call_llm()**: 处理速率限制错误和密钥轮换

### 文件结构

```
src/
├── utils/
│   ├── groq_api_manager.py  # API密钥管理器
│   └── llm.py              # 增强的LLM调用逻辑
├── llm/
│   └── models.py           # 修改的模型创建逻辑
└── ...
```

这个系统确保了在遇到GROQ API速率限制时，回测和其他操作能够自动切换到备用API密钥，从而保持系统的连续运行。
