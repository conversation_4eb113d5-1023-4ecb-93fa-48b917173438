import os
import json
from langchain_anthropic import ChatAnthropic
from langchain_deepseek import ChatDeepSeek
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_groq import ChatGroq
from langchain_openai import ChatOpenAI
from langchain_ollama import ChatOllama
from enum import Enum
from pydantic import BaseModel
from typing import <PERSON>ple, List
from pathlib import Path


class ModelProvider(str, Enum):
    """Enum for supported LLM providers"""

    ANTHROPIC = "Anthropic"
    DEEPSEEK = "DeepSeek"
    GEMINI = "Gemini"
    GROQ = "Groq"
    OPENAI = "OpenAI"
    OPENROUTER = "OpenRouter"
    OLLAMA = "Ollama"


class LLMModel(BaseModel):
    """Represents an LLM model configuration"""

    display_name: str
    model_name: str
    provider: ModelProvider

    def to_choice_tuple(self) -> Tuple[str, str, str]:
        """Convert to format needed for questionary choices"""
        return (self.display_name, self.model_name, self.provider.value)

    def is_custom(self) -> bool:
        """Check if the model is a Gemini model"""
        return self.model_name == "-"

    def has_json_mode(self) -> bool:
        """Check if the model supports JSON mode"""
        if self.is_deepseek() or self.is_gemini():
            return False
        # Only certain Ollama models support JSON mode
        if self.is_ollama():
            return "llama3" in self.model_name or "neural-chat" in self.model_name
        return True

    def is_deepseek(self) -> bool:
        """Check if the model is a DeepSeek model"""
        return self.model_name.startswith("deepseek")

    def is_gemini(self) -> bool:
        """Check if the model is a Gemini model"""
        return self.model_name.startswith("gemini")

    def is_ollama(self) -> bool:
        """Check if the model is an Ollama model"""
        return self.provider == ModelProvider.OLLAMA


# Load models from JSON file
def load_models_from_json(json_path: str) -> List[LLMModel]:
    """Load models from a JSON file"""
    with open(json_path, 'r') as f:
        models_data = json.load(f)

    models = []
    for model_data in models_data:
        # Convert string provider to ModelProvider enum
        provider_enum = ModelProvider(model_data["provider"])
        models.append(
            LLMModel(
                display_name=model_data["display_name"],
                model_name=model_data["model_name"],
                provider=provider_enum
            )
        )
    return models


# Get the path to the JSON files
current_dir = Path(__file__).parent
models_json_path = current_dir / "api_models.json"
ollama_models_json_path = current_dir / "ollama_models.json"

# Load available models from JSON
AVAILABLE_MODELS = load_models_from_json(str(models_json_path))

# Load Ollama models from JSON
OLLAMA_MODELS = load_models_from_json(str(ollama_models_json_path))

# Create LLM_ORDER in the format expected by the UI
LLM_ORDER = [model.to_choice_tuple() for model in AVAILABLE_MODELS]

# Create Ollama LLM_ORDER separately
OLLAMA_LLM_ORDER = [model.to_choice_tuple() for model in OLLAMA_MODELS]


def get_model_info(model_name: str, model_provider: str) -> LLMModel | None:
    """Get model information by model_name"""
    all_models = AVAILABLE_MODELS + OLLAMA_MODELS
    return next((model for model in all_models if model.model_name == model_name and model.provider == model_provider), None)


def get_deterministic_model_params():
    """Get deterministic parameters for LLM models"""
    return {
        "temperature": 0.0,
        # Removed top_p to test impact on determinism
        "seed": 42,    # Fixed seed for reproducibility
    }

def get_model(model_name: str, model_provider: ModelProvider):
    # Get deterministic parameters
    deterministic_params = get_deterministic_model_params()

    if model_provider == ModelProvider.GROQ:
        # 使用GROQ API管理器获取当前可用的API密钥
        from src.utils.groq_api_manager import get_groq_api_key
        api_key = get_groq_api_key()

        if not api_key:
            # Print error to console
            print(f"API Key Error: Please make sure GROQ_API_KEY1, GROQ_API_KEY2, GROQ_API_KEY3, or GROQ_API_KEY4 is set in your .env file.")
            raise ValueError("Groq API key not found. Please make sure at least one GROQ_API_KEY is set in your .env file.")

        # Groq-specific deterministic parameters
        groq_params = {
            "model": model_name,
            "api_key": api_key,
            "temperature": deterministic_params["temperature"],
            "model_kwargs": {
                "seed": deterministic_params["seed"]  # Groq requires seed in model_kwargs
            }
        }
        # Try with seed in model_kwargs (correct way for Groq)
        try:
            return ChatGroq(**groq_params)
        except Exception:
            # Fallback if seed parameter is not supported
            fallback_params = groq_params.copy()
            fallback_params["model_kwargs"] = {}  # Remove seed
            return ChatGroq(**fallback_params)

    elif model_provider == ModelProvider.OPENAI:
        # Get and validate API key
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            # Print error to console
            print(f"API Key Error: Please make sure OPENAI_API_KEY is set in your .env file.")
            raise ValueError("OpenAI API key not found.  Please make sure OPENAI_API_KEY is set in your .env file.")

        return ChatOpenAI(
            model=model_name,
            api_key=api_key,
            temperature=deterministic_params["temperature"],
            seed=deterministic_params["seed"]
        )

    elif model_provider == ModelProvider.OPENROUTER:
        # Get and validate API key
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            # Print error to console
            print(f"API Key Error: Please make sure OPENROUTER_API_KEY is set in your .env file.")
            raise ValueError("OpenRouter API key not found.  Please make sure OPENROUTER_API_KEY is set in your .env file.")

        # OpenRouter uses OpenAI-compatible API with custom base URL
        return ChatOpenAI(
            model=model_name,
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1",
            temperature=deterministic_params["temperature"],
            seed=deterministic_params["seed"]
        )

    elif model_provider == ModelProvider.ANTHROPIC:
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if not api_key:
            print(f"API Key Error: Please make sure ANTHROPIC_API_KEY is set in your .env file.")
            raise ValueError("Anthropic API key not found.  Please make sure ANTHROPIC_API_KEY is set in your .env file.")

        # Anthropic-specific deterministic parameters
        anthropic_params = {
            "model": model_name,
            "api_key": api_key,
            "temperature": deterministic_params["temperature"],
        }
        # Add seed if supported by Anthropic
        try:
            return ChatAnthropic(**anthropic_params, seed=deterministic_params["seed"])
        except TypeError:
            # Fallback if seed parameter is not supported
            return ChatAnthropic(**anthropic_params)

    elif model_provider == ModelProvider.DEEPSEEK:
        api_key = os.getenv("DEEPSEEK_API_KEY")
        if not api_key:
            print(f"API Key Error: Please make sure DEEPSEEK_API_KEY is set in your .env file.")
            raise ValueError("DeepSeek API key not found.  Please make sure DEEPSEEK_API_KEY is set in your .env file.")

        # DeepSeek-specific deterministic parameters
        deepseek_params = {
            "model": model_name,
            "api_key": api_key,
            "temperature": deterministic_params["temperature"],
        }
        # Add seed if supported by DeepSeek
        try:
            return ChatDeepSeek(**deepseek_params, seed=deterministic_params["seed"])
        except TypeError:
            # Fallback if seed parameter is not supported
            return ChatDeepSeek(**deepseek_params)

    elif model_provider == ModelProvider.GEMINI:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print(f"API Key Error: Please make sure GOOGLE_API_KEY is set in your .env file.")
            raise ValueError("Google API key not found.  Please make sure GOOGLE_API_KEY is set in your .env file.")

        # Gemini-specific deterministic parameters
        # Note: Gemini does not support seed parameter, so we exclude it
        gemini_params = {
            "model": model_name,
            "api_key": api_key,
            "temperature": deterministic_params["temperature"],
            "transport": "rest",  # Critical for gemini-2.0-flash model
        }
        # Gemini does not support seed parameter, so we don't include it
        return ChatGoogleGenerativeAI(**gemini_params)

    elif model_provider == ModelProvider.OLLAMA:
        # For Ollama, we use a base URL instead of an API key
        # Check if OLLAMA_HOST is set (for Docker on macOS)
        ollama_host = os.getenv("OLLAMA_HOST", "localhost")
        base_url = os.getenv("OLLAMA_BASE_URL", f"http://{ollama_host}:11434")

        return ChatOllama(
            model=model_name,
            base_url=base_url,
            temperature=deterministic_params["temperature"],
            seed=deterministic_params["seed"],
        )
