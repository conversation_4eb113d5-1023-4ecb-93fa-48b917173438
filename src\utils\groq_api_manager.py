"""GROQ API密钥管理器，支持多个API密钥的轮换以避免速率限制"""

import os
import logging
from typing import List, Optional
import threading

logger = logging.getLogger(__name__)


class GroqApiManager:
    """GROQ API密钥管理器，支持自动轮换API密钥以避免速率限制"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式确保全局只有一个API管理器实例"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化API密钥管理器"""
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.api_keys: List[str] = []
        self.current_key_index: int = 0
        self.failed_keys: set = set()  # 记录失败的API密钥
        self._load_api_keys()
    
    def _load_api_keys(self):
        """从环境变量加载所有GROQ API密钥"""
        # 加载所有GROQ API密钥
        key_names = ['GROQ_API_KEY1', 'GROQ_API_KEY2', 'GROQ_API_KEY3', 'GROQ_API_KEY4']
        
        for key_name in key_names:
            api_key = os.getenv(key_name)
            if api_key and api_key.strip():
                self.api_keys.append(api_key.strip())
                logger.info(f"已加载 {key_name}")
        
        # 如果没有找到编号的密钥，尝试加载默认的GROQ_API_KEY
        if not self.api_keys:
            default_key = os.getenv('GROQ_API_KEY')
            if default_key and default_key.strip():
                self.api_keys.append(default_key.strip())
                logger.info("已加载默认 GROQ_API_KEY")
        
        if not self.api_keys:
            logger.warning("未找到任何GROQ API密钥")
        else:
            logger.info(f"总共加载了 {len(self.api_keys)} 个GROQ API密钥")
    
    def get_current_api_key(self) -> Optional[str]:
        """获取当前可用的API密钥"""
        if not self.api_keys:
            logger.error("没有可用的GROQ API密钥")
            return None
        
        # 如果当前密钥已失败，尝试下一个
        attempts = 0
        while attempts < len(self.api_keys):
            current_key = self.api_keys[self.current_key_index]
            
            # 如果当前密钥没有失败，返回它
            if current_key not in self.failed_keys:
                logger.debug(f"使用API密钥索引: {self.current_key_index + 1}")
                return current_key
            
            # 当前密钥已失败，尝试下一个
            self._switch_to_next_key()
            attempts += 1
        
        # 所有密钥都失败了，重置失败记录并返回第一个密钥
        logger.warning("所有API密钥都已失败，重置失败记录")
        self.failed_keys.clear()
        self.current_key_index = 0
        return self.api_keys[0] if self.api_keys else None
    
    def _switch_to_next_key(self):
        """切换到下一个API密钥"""
        if len(self.api_keys) > 1:
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            logger.info(f"切换到API密钥索引: {self.current_key_index + 1}")
    
    def mark_key_as_failed(self, api_key: str):
        """标记API密钥为失败状态"""
        if api_key in self.api_keys:
            self.failed_keys.add(api_key)
            key_index = self.api_keys.index(api_key) + 1
            logger.warning(f"API密钥 {key_index} 已标记为失败")
    
    def handle_rate_limit_error(self, error_message: str) -> bool:
        """
        处理速率限制错误，切换到下一个API密钥
        
        Args:
            error_message: 错误消息
            
        Returns:
            bool: 如果成功切换到新的API密钥返回True，否则返回False
        """
        current_key = self.get_current_api_key()
        if current_key:
            # 标记当前密钥为失败
            self.mark_key_as_failed(current_key)
            
            # 切换到下一个密钥
            self._switch_to_next_key()
            
            # 检查是否有可用的密钥
            next_key = self.get_current_api_key()
            if next_key and next_key != current_key:
                logger.info(f"由于速率限制错误，已切换到新的API密钥")
                return True
        
        logger.error("无法切换到新的API密钥，所有密钥可能都已失败")
        return False
    
    def reset_failed_keys(self):
        """重置所有失败的密钥状态"""
        self.failed_keys.clear()
        logger.info("已重置所有失败的API密钥状态")
    
    def get_available_keys_count(self) -> int:
        """获取可用API密钥的数量"""
        return len(self.api_keys) - len(self.failed_keys)
    
    def get_status(self) -> dict:
        """获取API管理器的状态信息"""
        return {
            "total_keys": len(self.api_keys),
            "current_key_index": self.current_key_index + 1 if self.api_keys else 0,
            "failed_keys_count": len(self.failed_keys),
            "available_keys_count": self.get_available_keys_count()
        }


# 全局API管理器实例
groq_api_manager = GroqApiManager()


def get_groq_api_key() -> Optional[str]:
    """获取当前可用的GROQ API密钥"""
    return groq_api_manager.get_current_api_key()


def handle_groq_rate_limit(error_message: str) -> bool:
    """处理GROQ速率限制错误"""
    return groq_api_manager.handle_rate_limit_error(error_message)


def is_groq_rate_limit_error(error_message: str) -> bool:
    """检查是否为GROQ速率限制错误"""
    error_lower = error_message.lower()
    rate_limit_indicators = [
        "rate limit reached",
        "rate limit exceeded", 
        "429",
        "requests per day",
        "rpd",
        "too many requests"
    ]
    
    return any(indicator in error_lower for indicator in rate_limit_indicators)
